<?php
/**
 * Script de test pour la restauration sécurisée de backups
 * Ce script teste les nouvelles fonctionnalités de backup/restore
 */

require_once __DIR__ . '/lib/backup.php';

echo "=== Test de la restauration sécurisée de backups ===\n\n";

// Test 1: Vérifier que la fonction dropAllTables existe
echo "Test 1: Vérification de la fonction dropAllTables\n";
if (method_exists('Backup', 'dropAllTables')) {
    echo "✓ La fonction dropAllTables existe\n";
} else {
    echo "✗ La fonction dropAllTables n'existe pas\n";
}

// Test 2: Vérifier que la fonction restoreBackupSafe existe
echo "\nTest 2: Vérification de la fonction restoreBackupSafe\n";
if (method_exists('Backup', 'restoreBackupSafe')) {
    echo "✓ La fonction restoreBackupSafe existe\n";
} else {
    echo "✗ La fonction restoreBackupSafe n'existe pas\n";
}

// Test 3: Créer une backup de test
echo "\nTest 3: Création d'une backup de test\n";
$testBackupPath = __DIR__ . '/backups/test_backup_' . date('Y-m-d_H-i-s') . '.sql';
$backupResult = Backup::createBackup($testBackupPath);

if ($backupResult['success']) {
    echo "✓ Backup de test créée: " . $backupResult['file_path'] . "\n";
    echo "  Taille: " . round($backupResult['file_size'] / 1024, 2) . " KB\n";
} else {
    echo "✗ Erreur lors de la création de la backup: " . $backupResult['message'] . "\n";
}

// Test 4: Lister les backups disponibles
echo "\nTest 4: Liste des backups disponibles\n";
$backups = Backup::listBackups();
echo "Nombre de backups trouvées: " . count($backups) . "\n";

foreach ($backups as $backup) {
    echo "  - " . $backup['filename'] . " (" . round($backup['size'] / 1024, 2) . " KB)\n";
}

// Test 5: Test de validation des paramètres
echo "\nTest 5: Test de validation des paramètres\n";

// Test sans confirmation
$result = Backup::restoreBackupSafe('/fake/path.sql', false);
if (!$result['success'] && strpos($result['message'], 'confirmation') !== false) {
    echo "✓ Validation de la confirmation fonctionne\n";
} else {
    echo "✗ Validation de la confirmation échoue\n";
}

// Test avec fichier inexistant
$result = Backup::restoreBackupSafe('/fake/path.sql', true);
if (!$result['success'] && strpos($result['message'], 'existe pas') !== false) {
    echo "✓ Validation de l'existence du fichier fonctionne\n";
} else {
    echo "✗ Validation de l'existence du fichier échoue\n";
}

echo "\n=== Tests terminés ===\n";

// Afficher les informations de configuration
echo "\nInformations de configuration:\n";
try {
    $reflection = new ReflectionClass('Backup');
    $method = $reflection->getMethod('getDatabaseConfig');
    $method->setAccessible(true);
    $dbConfig = $method->invoke(null);
    
    echo "  Host: " . $dbConfig['host'] . "\n";
    echo "  Database: " . $dbConfig['database'] . "\n";
    echo "  User: " . $dbConfig['user'] . "\n";
} catch (Exception $e) {
    echo "  Erreur lors de la récupération de la config: " . $e->getMessage() . "\n";
}

// Afficher l'espace disque
echo "\nEspace disque:\n";
$diskSpace = Backup::checkDiskSpace(__DIR__ . '/backups');
if ($diskSpace['success']) {
    echo "  Espace libre: " . $diskSpace['free_space_gb'] . " GB\n";
    echo "  Espace total: " . $diskSpace['total_space_gb'] . " GB\n";
} else {
    echo "  Erreur: " . $diskSpace['message'] . "\n";
}

echo "\n=== Fin du script de test ===\n";
?>
